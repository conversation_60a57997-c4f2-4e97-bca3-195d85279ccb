# 🎲 Pig Game

A classic dice game implementation built with vanilla JavaScript, HTML, and CSS. This project serves as a practical exercise in DOM manipulation, event handling, and game logic implementation.

## 🎯 About the Game

The Pig Game is a simple yet engaging two-player dice game where players race to reach 100 points first. It's a game of strategy and risk management - do you play it safe or push your luck for higher scores?

### Game Rules

1. **Two players take turns** rolling a single dice
2. **On each turn**, a player can:
   - **Roll the dice**: Add the dice value to their current round score
   - **Hold**: Add their current round score to their total score and end their turn
3. **Special rule**: If a player rolls a 1, they lose all points from their current round and their turn ends
4. **Winning condition**: First player to reach 100 total points wins the game

### Strategy Element
The core decision: Keep rolling to accumulate more points (risking a 1 and losing everything), or hold and secure your current round points?

## 🚀 Getting Started

### Prerequisites
- A modern web browser (Chrome, Firefox, Safari, Edge)
- No additional installations required!

### Running the Game
1. Clone or download this repository
2. Navigate to the `pigGame` folder
3. Open `index.html` in your web browser
4. Start playing immediately!

```bash
# If you have a local server (optional)
# Navigate to the pigGame directory and run:
python -m http.server 8000
# Then visit http://localhost:8000
```

## 🛠️ Technologies Used

- **HTML5**: Semantic structure and game layout
- **CSS3**: Modern styling with gradients, backdrop filters, and smooth transitions
- **Vanilla JavaScript**: Game logic, DOM manipulation, and event handling
- **Google Fonts**: Nunito font family for clean typography

## 📁 Project Structure

```
pigGame/
├── index.html              # Main HTML structure
├── script.js               # Game logic and DOM manipulation
├── style.css               # Styling and responsive design
├── dice-1.png through      # Dice face images (1-6)
│   dice-6.png
├── pig-game-flowchart.png  # Game logic flowchart
└── README.md               # This file
```

## 🎮 Features

- **Interactive UI**: Clean, modern interface with visual feedback
- **Responsive Design**: Adapts to different screen sizes
- **Smooth Animations**: CSS transitions for player switches and button interactions
- **Visual Dice**: Actual dice images that change based on rolls
- **Game State Management**: Proper handling of active players, scores, and game end conditions
- **Reset Functionality**: Start a new game at any time

## 🧠 Learning Objectives

This project demonstrates several key JavaScript concepts:

- **DOM Manipulation**: Selecting and modifying HTML elements
- **Event Handling**: Responding to user clicks and interactions
- **State Management**: Tracking game state with variables
- **Conditional Logic**: Implementing game rules with if/else statements
- **Functions**: Organizing code into reusable functions
- **CSS Classes**: Dynamically adding/removing classes for styling
- **Random Number Generation**: Creating dice roll functionality

## 🎨 Alternative Game Names

Here are some creative alternatives to "Pig Game":

1. **🎲 Dice Dare** - Emphasizes the risk-taking element
2. **🏃‍♂️ Rush to 100** - Highlights the race aspect
3. **⚡ Risk & Roll** - Captures the strategy and chance elements
4. **🎯 Hold or Fold** - References the key decision players make
5. **🔥 Lucky Streak** - Focuses on the luck and momentum aspects

## 🎓 Code Highlights

### Game Initialization
<augment_code_snippet path="pigGame/script.js" mode="EXCERPT">
````javascript
const init = function () {
  scores = [0, 0];
  currentScore = 0;
  activePlayer = 0;
  playing = true;
  // Reset UI elements...
};
````
</augment_code_snippet>

### Player Switching Logic
<augment_code_snippet path="pigGame/script.js" mode="EXCERPT">
````javascript
const switchPlayer = function () {
  document.getElementById(`current--${activePlayer}`).textContent = 0;
  currentScore = 0;
  activePlayer = activePlayer === 0 ? 1 : 0;
  player0El.classList.toggle('player--active');
  player1El.classList.toggle('player--active');
};
````
</augment_code_snippet>

## 🔄 Future Enhancements

Potential improvements for continued learning:

- Add player name customization
- Implement different winning score options
- Add sound effects and animations
- Create AI opponent mode
- Add game statistics tracking
- Implement local storage for high scores

## 📚 Learning Path Context

This project is part of a JavaScript learning journey, building upon backend experience with Spring Boot microservices. It focuses on:
- Frontend fundamentals
- Client-side interactivity
- Modern JavaScript practices
- Clean code organization

---

*Built as a learning exercise in vanilla JavaScript development*
